CREATE TABLE article_property (
    art_id INTEGER NOT NULL,
    propertykeyvalue_id INTEGER NOT NULL,
    sort INTEGER NOT NULL,
    CONSTRAINT pk_article_property PRIMARY KEY (art_id, propertykeyvalue_id)
);

-- =======================================================================
CREATE TABLE campaign_property (
    camp_id INTEGER NOT NULL,
    propertykeyvalue_id INTEGER NOT NULL,
    sort INTEGER NOT NULL,
    CONSTRAINT pk_campaign_property PRIMARY KEY (camp_id, propertykeyvalue_id)
);

-- =======================================================================

CREATE TABLE propertykey (
    propertykey_id SERIAL NOT NULL,
    propertykey_name VARCHAR(100) NOT NULL,
    sort INTEGER NOT NULL,
    keytype SMALLINT NOT NULL,
    CONSTRAINT pk_propertykey PRIMARY KEY (propertykey_id),
    CONSTRAINT uk_propertykey_name_keytype UNIQUE (propertykey_name, keytype)
);

-- =======================================================================

CREATE TABLE propertykey_translation (
    propertykey_id INTEGER NOT NULL,
    lang_id SMALLINT NOT NULL,
    propertykey_name_translation VARCHAR(100) NOT NULL,
    propertykey_desc_translation TEXT NOT NULL,
    CONSTRAINT pk_propertykey_name PRIMARY KEY (propertykey_id, lang_id)
);

-- =======================================================================
CREATE TABLE propertyvalue (
    propertyvalue_id SERIAL NOT NULL,
    propertyvalue TEXT NOT NULL,
    CONSTRAINT pk_propertyvalue PRIMARY KEY (propertyvalue_id)
);

-- =======================================================================
CREATE TABLE propertyvalue_translation (
    propertyvalue_id INTEGER NOT NULL,
    lang_id SMALLINT NOT NULL,
    propertyvalue_translation TEXT NOT NULL,
    propertyvalue_desc_translation TEXT NOT NULL,
    CONSTRAINT pk_propertyvalue_name PRIMARY KEY (propertyvalue_id, lang_id)
);

-- =======================================================================
CREATE TABLE propertykey_value (
    propertykeyvalue_id SERIAL NOT NULL,
    propertykey_id INTEGER NOT NULL,
    propertyvalue_id INTEGER NOT NULL,
    CONSTRAINT pk_propertykey_value PRIMARY KEY (propertykeyvalue_id)
);

-- =======================================================================
-- Table type for property input parameters
-- =======================================================================
CREATE TYPE property_input_type AS (
    propertykey_name VARCHAR(100),
    propertyvalue TEXT,
    lang_id SMALLINT,
    propertykey_name_translation VARCHAR(100),
    propertyvalue_translation TEXT
);

-- =======================================================================
-- Stored procedure to upsert entity properties
-- =======================================================================
CREATE OR REPLACE FUNCTION upsert_entity_properties(
    p_keytype SMALLINT,
    p_entity_id INTEGER,
    p_properties property_input_type[]
)
RETURNS VOID AS $$
DECLARE
    property_rec property_input_type;
    v_propertykey_id INTEGER;
    v_propertyvalue_id INTEGER;
    v_propertykeyvalue_id INTEGER;
    v_sort INTEGER;
BEGIN
    -- Loop through each property in the input array
    FOREACH property_rec IN ARRAY p_properties
    LOOP
        -- 1. Upsert propertykey
        -- First try to get existing propertykey_id
        SELECT propertykey_id INTO v_propertykey_id
        FROM propertykey
        WHERE propertykey_name = property_rec.propertykey_name
        AND keytype = p_keytype;

        -- If not found, insert new propertykey
        IF v_propertykey_id IS NULL THEN
            INSERT INTO propertykey (propertykey_name, sort, keytype)
            VALUES (property_rec.propertykey_name, 0, p_keytype)
            RETURNING propertykey_id INTO v_propertykey_id;
        END IF;

        -- 2. Upsert propertykey_translation if translation data provided
        IF property_rec.propertykey_name_translation IS NOT NULL AND property_rec.lang_id IS NOT NULL THEN
            INSERT INTO propertykey_translation (propertykey_id, lang_id, propertykey_name_translation, propertykey_desc_translation)
            VALUES (v_propertykey_id, property_rec.lang_id, property_rec.propertykey_name_translation, '')
            ON CONFLICT (propertykey_id, lang_id) DO UPDATE SET
                propertykey_name_translation = EXCLUDED.propertykey_name_translation;
        END IF;

        -- 3. Upsert propertyvalue
        -- First try to get existing propertyvalue_id
        SELECT propertyvalue_id INTO v_propertyvalue_id
        FROM propertyvalue
        WHERE propertyvalue = property_rec.propertyvalue;

        -- If not found, insert new propertyvalue
        IF v_propertyvalue_id IS NULL THEN
            INSERT INTO propertyvalue (propertyvalue)
            VALUES (property_rec.propertyvalue)
            RETURNING propertyvalue_id INTO v_propertyvalue_id;
        END IF;

        -- 4. Upsert propertyvalue_translation if translation data provided
        IF property_rec.propertyvalue_translation IS NOT NULL AND property_rec.lang_id IS NOT NULL THEN
            INSERT INTO propertyvalue_translation (propertyvalue_id, lang_id, propertyvalue_translation, propertyvalue_desc_translation)
            VALUES (v_propertyvalue_id, property_rec.lang_id, property_rec.propertyvalue_translation, '')
            ON CONFLICT (propertyvalue_id, lang_id) DO UPDATE SET
                propertyvalue_translation = EXCLUDED.propertyvalue_translation;
        END IF;

        -- 5. Upsert propertykey_value
        -- First try to get existing propertykeyvalue_id
        SELECT propertykeyvalue_id INTO v_propertykeyvalue_id
        FROM propertykey_value
        WHERE propertykey_id = v_propertykey_id
        AND propertyvalue_id = v_propertyvalue_id;

        -- If not found, insert new propertykey_value
        IF v_propertykeyvalue_id IS NULL THEN
            INSERT INTO propertykey_value (propertykey_id, propertyvalue_id)
            VALUES (v_propertykey_id, v_propertyvalue_id)
            RETURNING propertykeyvalue_id INTO v_propertykeyvalue_id;
        END IF;

        -- 6. Determine sort order for the entity property
        IF p_keytype = 1 THEN -- Article properties
            SELECT COALESCE(MAX(sort), 0) + 1 INTO v_sort
            FROM article_property
            WHERE art_id = p_entity_id;

            -- Upsert article_property
            INSERT INTO article_property (art_id, propertykeyvalue_id, sort)
            VALUES (p_entity_id, v_propertykeyvalue_id, v_sort)
            ON CONFLICT (art_id, propertykeyvalue_id) DO UPDATE SET
                sort = EXCLUDED.sort;

        ELSIF p_keytype = 2 THEN -- Campaign properties
            SELECT COALESCE(MAX(sort), 0) + 1 INTO v_sort
            FROM campaign_property
            WHERE camp_id = p_entity_id;

            -- Upsert campaign_property
            INSERT INTO campaign_property (camp_id, propertykeyvalue_id, sort)
            VALUES (p_entity_id, v_propertykeyvalue_id, v_sort)
            ON CONFLICT (camp_id, propertykeyvalue_id) DO UPDATE SET
                sort = EXCLUDED.sort;
        END IF;

    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =======================================================================
-- Example usage:
-- =======================================================================
/*
-- Example 1: Insert/Update article properties (keytype = 1)
SELECT upsert_entity_properties(
    1, -- keytype for articles
    123, -- article ID
    ARRAY[
        ROW('color', 'red', 1, 'Color', 'Rouge')::property_input_type,
        ROW('size', 'large', 1, 'Size', 'Grand')::property_input_type,
        ROW('brand', 'Nike', 2, 'Brand', 'Nike')::property_input_type
    ]
);

-- Example 2: Insert/Update campaign properties (keytype = 2)
SELECT upsert_entity_properties(
    2, -- keytype for campaigns
    456, -- campaign ID
    ARRAY[
        ROW('target_audience', 'adults', 1, 'Target Audience', 'Adultes')::property_input_type,
        ROW('budget', '10000', 1, 'Budget', '10000')::property_input_type
    ]
);

-- Example 3: Insert properties without translations
SELECT upsert_entity_properties(
    1, -- keytype for articles
    789, -- article ID
    ARRAY[
        ROW('material', 'cotton', NULL, NULL, NULL)::property_input_type,
        ROW('weight', '500g', NULL, NULL, NULL)::property_input_type
    ]
);
*/