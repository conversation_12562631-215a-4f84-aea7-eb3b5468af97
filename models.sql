CREATE TABLE article_property (
    art_id INTEGER NOT NULL,
    propertykeyvalue_id INTEGER NOT NULL,
    sort INTEGER NOT NULL,
    CONSTRAINT pk_article_property PRIMARY KEY (art_id, propertykeyvalue_id)
);

-- =======================================================================
CREATE TABLE campaign_property (
    camp_id INTEGER NOT NULL,
    propertykeyvalue_id INTEGER NOT NULL,
    sort INTEGER NOT NULL,
    CONSTRAINT pk_campaign_property PRIMARY KEY (camp_id, propertykeyvalue_id)
);

-- =======================================================================

CREATE TABLE propertykey (
    propertykey_id SERIAL NOT NULL,
    propertykey_name VARCHAR(100) NOT NULL,
    sort INTEGER NOT NULL,
    keytype SMALLINT NOT NULL,
    CONSTRAINT pk_propertykey PRIMARY KEY (propertykey_id)
);

-- =======================================================================

CREATE TABLE propertykey_translation (
    propertykey_id INTEGER NOT NULL,
    lang_id SMALLINT NOT NULL,
    propertykey_name_translation VARCHAR(100) NOT NULL,
    propertykey_desc_translation TEXT NOT NULL,
    CONSTRAINT pk_propertykey_name PRIMARY KEY (propertykey_id, lang_id)
);

-- =======================================================================
CREATE TABLE propertyvalue (
    propertyvalue_id SERIAL NOT NULL,
    propertyvalue TEXT NOT NULL,
    CONSTRAINT pk_propertyvalue PRIMARY KEY (propertyvalue_id)
);

-- =======================================================================
CREATE TABLE propertyvalue_translation (
    propertyvalue_id INTEGER NOT NULL,
    lang_id SMALLINT NOT NULL,
    propertyvalue_translation TEXT NOT NULL,
    propertyvalue_desc_translation TEXT NOT NULL,
    CONSTRAINT pk_propertyvalue_name PRIMARY KEY (propertyvalue_id, lang_id)
);

-- =======================================================================
CREATE TABLE propertykey_value (
    propertykeyvalue_id SERIAL NOT NULL,
    propertykey_id INTEGER NOT NULL,
    propertyvalue_id INTEGER NOT NULL,
    CONSTRAINT pk_propertykey_value PRIMARY KEY (propertykeyvalue_id)
);